package com.tool.pams.api.web.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tool.pams.business.service.repository.cmdb.PamsLogCleanRecordService;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordUpdateBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordPageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 应用日志清理记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@RestController
@RequestMapping("/pams-log-clean-record")
@Tag(name = "应用日志清理记录表")
@Validated
public class PamsLogCleanRecordController {

    @Resource
    private PamsLogCleanRecordService pamsLogCleanRecordService;

    @PostMapping("/save")
    @Operation(summary = "添加应用日志清理记录")
    public BaseResult<Boolean> save(@Valid @RequestBody PamsLogCleanRecordSaveBO saveBO) {
        return BaseResult.success(pamsLogCleanRecordService.saveInfo(saveBO));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除应用日志清理记录")
    public BaseResult<Boolean> delete(@PathVariable Long id) {
        return BaseResult.success(pamsLogCleanRecordService.delInfo(id));
    }

    @PutMapping("/update")
    @Operation(summary = "修改应用日志清理记录")
    public BaseResult<Boolean> update(@Valid @RequestBody PamsLogCleanRecordUpdateBO updateBO) {
        return BaseResult.success(pamsLogCleanRecordService.updateInfo(updateBO));
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取应用日志清理记录详情")
    public BaseResult<PamsLogCleanRecordDetailVO> detail(@PathVariable Long id) {
        return BaseResult.success(pamsLogCleanRecordService.getInfo(id));
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询应用日志清理记录")
    public BaseResult<IPage<PamsLogCleanRecordPageVO>> page(@Valid @RequestBody PamsLogCleanRecordQueryParamsBO queryParamsBO) {
        return BaseResult.success(pamsLogCleanRecordService.getPageInfo(queryParamsBO));
    }
}
